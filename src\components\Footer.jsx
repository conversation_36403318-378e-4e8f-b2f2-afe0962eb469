import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import './Footer.css'
import { companyInfo, footerLinks, certifications } from '../data/content'

const Footer = () => {
  const currentYear = new Date().getFullYear()
  const [showScrollTop, setShowScrollTop] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          <div className="footer-section">
            <h3>{companyInfo.name}</h3>
            <p>{companyInfo.description}</p>
            <div className="certifications">
              {certifications.map((cert, index) => (
                <span key={index} className="cert-badge">{cert}</span>
              ))}
            </div>
          </div>

          <div className="footer-section">
            <h4>Rychlé odkazy</h4>
            <ul>
              {footerLinks.map((link, index) => (
                <li key={index}><Link to={link.href}>{link.text}</Link></li>
              ))}
            </ul>
          </div>

          <div className="footer-section">
            <h4>Naše služby</h4>
            <ul>
              <li>Elektroinstalace</li>
              <li>Revize a kontroly</li>
              <li>Opravy a poruchy</li>
              <li>LED osvětlení</li>
              <li>Průmyslové instalace</li>
            </ul>
          </div>

          <div className="footer-section">
            <h4>Kontakt</h4>
            <div className="footer-contact">
              <p>📍 {companyInfo.contact.address.street}, {companyInfo.contact.address.city}</p>
              <p>📞 {companyInfo.contact.phone}</p>
              <p>✉️ {companyInfo.contact.email}</p>
            </div>
          </div>
        </div>

        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <p>&copy; {currentYear} Elektromontáže BARKTEK. Všechna práva vyhrazena.</p>
            {showScrollTop && (
              <button className="scroll-top" onClick={scrollToTop} aria-label="Zpět nahoru">
                ↑
              </button>
            )}
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
