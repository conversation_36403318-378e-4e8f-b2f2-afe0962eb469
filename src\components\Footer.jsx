import { useState, useEffect } from 'react'
import './Footer.css'

const Footer = () => {
  const currentYear = new Date().getFullYear()
  const [showScrollTop, setShowScrollTop] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          <div className="footer-section">
            <h3>ELEKTROMONTÁŽE BARKTEK</h3>
            <p>
              Profesionální elektrotechnické služby s více než 15letou zkušeností. 
              Specializujeme se na kvalitní a bezpečné elektroinstalace.
            </p>
            <div className="certifications">
              <span className="cert-badge">✓ Certifikovaná firma</span>
              <span className="cert-badge">✓ Pojištění odpovědnosti</span>
            </div>
          </div>

          <div className="footer-section">
            <h4>Rychlé odkazy</h4>
            <ul>
              <li><a href="#home">Domů</a></li>
              <li><a href="#services">Služby</a></li>
              <li><a href="#gallery">Galerie</a></li>
              <li><a href="#contact">Kontakt</a></li>
            </ul>
          </div>

          <div className="footer-section">
            <h4>Naše služby</h4>
            <ul>
              <li>Elektroinstalace</li>
              <li>Revize a kontroly</li>
              <li>Opravy a poruchy</li>
              <li>LED osvětlení</li>
              <li>Průmyslové instalace</li>
            </ul>
          </div>

          <div className="footer-section">
            <h4>Kontakt</h4>
            <div className="footer-contact">
              <p>📍 Hlavní 123, 123 45 Praha 1</p>
              <p>📞 +420 123 456 789</p>
              <p>✉️ <EMAIL></p>
              <p className="emergency">🚨 Pohotovost: +420 987 654 321</p>
            </div>
          </div>
        </div>

        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <p>&copy; {currentYear} Elektromontáže BARKTEK. Všechna práva vyhrazena.</p>
            {showScrollTop && (
              <button className="scroll-top" onClick={scrollToTop} aria-label="Zpět nahoru">
                ↑
              </button>
            )}
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
