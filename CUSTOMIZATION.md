# Návod na úpravu obsahu webu

Tento dokument obsahuje podrobné instrukce pro úpravu obsahu webu Elektromontáže BARKTEK.

## 📝 Úprava textů

### 1. <PERSON><PERSON><PERSON><PERSON> nadpis a popis (Hero sekce)
**Soubor:** `src/components/Hero.jsx`

```jsx
<h1>Profesionální elektromontáže</h1>
<h2>BARKTEK</h2>
<p>
  Specializujeme se na komplexní elektroinstalace, revize elektrických zařízení 
  a opravy pro domácnosti i firmy. Naše zkušený tým garantuje kvalitu a bezpečnost.
</p>
```

### 2. Služby
**Soubor:** `src/components/Services.jsx`

Najděte pole `services` a upravte jednotlivé služby:

```jsx
const services = [
  {
    icon: '🏠',
    title: 'Elektroinstalace v domácnostech',
    description: '<PERSON><PERSON><PERSON> popis služby...',
    features: ['Funkce 1', 'Funkce 2', 'Funkce 3']
  },
  // ... dal<PERSON><PERSON> služ<PERSON>
]
```

### 3. Kontaktní údaje
**Soubory:** `src/components/Contact.jsx` a `src/components/Footer.jsx`

Upravte tyto údaje:
- Adresa
- Telefon
- Email
- Pracovní doba
- Pohotovostní číslo

## 🖼️ Úprava obrázků v galerii

### Možnost 1: Použití externích obrázků (současný stav)
**Soubor:** `src/components/Gallery.jsx`

```jsx
const galleryImages = [
  {
    id: 1,
    src: 'URL_VAŠEHO_OBRÁZKU',
    alt: 'Popis obrázku',
    title: 'Název projektu'
  },
  // ... další obrázky
]
```

### Možnost 2: Použití lokálních obrázků
1. Vytvořte složku `public/images/`
2. Nahrajte své obrázky (doporučené formáty: JPG, PNG, WebP)
3. Upravte cesty v `Gallery.jsx`:

```jsx
const galleryImages = [
  {
    id: 1,
    src: '/images/projekt1.jpg',
    alt: 'Elektroinstalace v rodinném domě',
    title: 'Kompletní elektroinstalace RD'
  },
  // ... další obrázky
]
```

## 🎨 Úprava barev a stylů

### Hlavní barvy
**Soubor:** `src/index.css`

```css
:root {
  --primary-color: #1e3c72;    /* Hlavní modrá */
  --secondary-color: #2a5298;  /* Světlejší modrá */
  --accent-color: #ffd700;     /* Zlatá */
  --text-dark: #333;           /* Tmavý text */
  --text-light: #666;          /* Světlý text */
  --background-light: #f8f9fa; /* Světlé pozadí */
}
```

### Změna fontu
V `src/index.css` změňte import Google Fonts:

```css
@import url('https://fonts.googleapis.com/css2?family=VÁŠE_PÍSMO:wght@300;400;500;600;700;800&display=swap');
```

A pak upravte font-family:

```css
:root {
  font-family: 'VÁŠE_PÍSMO', system-ui, Arial, sans-serif;
}
```

## 📱 Úprava meta tagů a SEO

**Soubor:** `index.html`

```html
<meta name="description" content="Váš popis firmy pro vyhledávače" />
<title>Váš název - Váš slogan</title>
```

## 🔧 Přidání nových sekcí

### Vytvoření nové komponenty
1. Vytvořte nový soubor v `src/components/`, např. `About.jsx`
2. Vytvořte odpovídající CSS soubor `About.css`
3. Přidejte komponentu do `src/App.jsx`:

```jsx
import About from './components/About'

function App() {
  return (
    <div className="App">
      <Header />
      <Hero />
      <About />  {/* Nová sekce */}
      <Services />
      {/* ... ostatní komponenty */}
    </div>
  )
}
```

4. Přidejte odkaz do navigace v `Header.jsx`

## 📞 Funkční kontaktní formulář

Pro funkční formulář doporučujeme:

### Možnost 1: EmailJS (zdarma)
1. Zaregistrujte se na [EmailJS](https://www.emailjs.com/)
2. Nainstalujte: `npm install @emailjs/browser`
3. Upravte `Contact.jsx` podle dokumentace EmailJS

### Možnost 2: Backend API
Vytvořte vlastní backend nebo použijte služby jako:
- Netlify Forms
- Formspree
- Getform

## 🚀 Nasazení webu

### Netlify (doporučeno)
1. Zaregistrujte se na [Netlify](https://netlify.com)
2. Připojte GitHub repozitář
3. Nastavte build command: `npm run build`
4. Nastavte publish directory: `dist`

### Vercel
1. Zaregistrujte se na [Vercel](https://vercel.com)
2. Importujte projekt z GitHubu
3. Automatické nasazení při každém commitu

## 📊 Přidání Google Analytics

1. Vytvořte účet na [Google Analytics](https://analytics.google.com)
2. Získejte tracking ID
3. Přidejte do `index.html`:

```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

## 🔍 Tipy pro optimalizaci

1. **Obrázky**: Komprimujte obrázky před nahráním (doporučeno < 500KB)
2. **Performance**: Použijte WebP formát pro obrázky
3. **SEO**: Přidejte alt texty ke všem obrázkům
4. **Rychlost**: Minimalizujte počet externích fontů

## 🆘 Časté problémy

### Web se nenačítá
- Zkontrolujte, zda běží `npm run dev`
- Zkontrolujte konzoli prohlížeče (F12)

### Obrázky se nezobrazují
- Zkontrolujte cesty k obrázkům
- Ujistěte se, že jsou obrázky ve správné složce

### Formulář nefunguje
- Implementujte backend nebo použijte EmailJS
- Zkontrolujte konzoli pro chyby

## 📞 Podpora

Pro další pomoc s úpravami kontaktujte vývojáře nebo se podívejte do dokumentace React a Vite.
