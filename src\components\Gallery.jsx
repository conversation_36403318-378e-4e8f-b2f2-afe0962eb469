import { useState } from 'react'
import './Gallery.css'
import { galleryImages } from '../data/content'

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState(null)

  const openModal = (image) => {
    setSelectedImage(image)
  }

  const closeModal = () => {
    setSelectedImage(null)
  }

  return (
    <section id="gallery" className="gallery">
      <div className="container">
        <div className="section-header">
          <h2>Galerie našich prací</h2>
          <p>Podívejte se na ukázky našich realizovaných projektů</p>
        </div>
        
        <div className="gallery-grid">
          {galleryImages.map((image) => (
            <div key={image.id} className="gallery-item" onClick={() => openModal(image)}>
              <img src={image.src} alt={image.alt} />
              <div className="gallery-overlay">
                <h4>{image.title}</h4>
                <span className="view-icon">👁️</span>
              </div>
            </div>
          ))}
        </div>

        {selectedImage && (
          <div className="modal" onClick={closeModal}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <span className="close" onClick={closeModal}>&times;</span>
              <img src={selectedImage.src} alt={selectedImage.alt} />
              <h3>{selectedImage.title}</h3>
            </div>
          </div>
        )}
      </div>
    </section>
  )
}

export default Gallery
