.career {
  padding: 5rem 0;
  background: white;
}

.career .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.career-intro {
  margin-bottom: 4rem;
}

.intro-content h3 {
  text-align: center;
  font-size: 2rem;
  color: #1e3c72;
  margin-bottom: 2rem;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.benefit-item {
  text-align: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 10px;
  transition: transform 0.3s ease;
}

.benefit-item:hover {
  transform: translateY(-5px);
}

.benefit-item h4 {
  color: #1e3c72;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.benefit-item p {
  color: #666;
  margin: 0;
  font-size: 0.9rem;
}

.positions-section {
  margin-bottom: 4rem;
}

.positions-section h3 {
  text-align: center;
  font-size: 2rem;
  color: #1e3c72;
  margin-bottom: 3rem;
}

.positions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.position-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.position-card:hover {
  border-color: #1e3c72;
  box-shadow: 0 10px 30px rgba(30, 60, 114, 0.1);
}

.position-header {
  margin-bottom: 1.5rem;
}

.position-header h4 {
  color: #1e3c72;
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.position-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.position-type,
.position-location {
  background: #e9ecef;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.9rem;
  color: #666;
}

.position-type {
  background: #d4edda;
  color: #155724;
}

.position-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.position-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.requirements h5,
.benefits h5 {
  color: #1e3c72;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.requirements ul,
.benefits ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirements li,
.benefits li {
  padding: 0.3rem 0;
  color: #666;
  position: relative;
  padding-left: 1.5rem;
  font-size: 0.9rem;
}

.requirements li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #1e3c72;
  font-weight: bold;
}

.benefits li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}

.apply-button {
  background: linear-gradient(135deg, #1e3c72, #2a5298);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.apply-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(30, 60, 114, 0.3);
}

.contact-hr {
  text-align: center;
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 15px;
}

.contact-hr h3 {
  color: #1e3c72;
  margin-bottom: 1rem;
}

.contact-hr p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.contact-hr strong {
  color: #1e3c72;
}

/* Mobile styles */
@media (max-width: 768px) {
  .career {
    padding: 3rem 0;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .positions-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .position-details {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .position-card {
    padding: 1.5rem;
  }

  .intro-content h3,
  .positions-section h3 {
    font-size: 1.5rem;
  }

  .position-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .position-type,
  .position-location {
    width: fit-content;
  }
}
