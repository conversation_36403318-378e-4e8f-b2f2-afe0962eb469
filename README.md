# Elektromontáže BARKTEK - Webové stránky

Moderní responzivní webové stránky pro firmu Elektromontáže BARKTEK vytvořené v React.js s Vite.

## 🚀 Funkce

- **Responzivní design** - optimalizováno pro všechna zařízení
- **Moderní UI/UX** - čistý a profesionální vzhled
- **Interaktivní galerie** - s modal okny pro zobrazení obrázků
- **Kontaktní formulář** - pro snadnou komunikaci s klienty
- **Smooth scrolling** - plynulé přecházení mezi sekcemi
- **SEO optimalizace** - meta tagy a strukturované data

## 📋 Sekce webu

1. **Header** - navigační menu s logem
2. **Hero** - hlavní sekce s představením firmy
3. **Služby** - přehled poskytovaných služeb
4. **Galerie** - fotografie realizovaných projektů
5. **Kontakt** - kontaktní informace a formulář
6. **Footer** - dodatečné informace a odkazy

## 🛠️ Technologie

- **React 18** - moderní JavaScript framework
- **Vite** - rychlý build tool
- **CSS3** - moderní styly s flexbox a grid
- **Google Fonts** - typografie Inter

## 🚀 Spuštění projektu

### Předpoklady
- Node.js 18+ (nebo použijte WSL)
- npm nebo yarn

### Instalace a spuštění

```bash
# Instalace závislostí
npm install

# Spuštění vývojového serveru
npm run dev

# Build pro produkci
npm run build

# Preview produkční verze
npm run preview
```

## 📁 Struktura projektu

```
src/
├── components/          # React komponenty
│   ├── Header.jsx      # Navigační header
│   ├── Hero.jsx        # Hlavní sekce
│   ├── Services.jsx    # Služby
│   ├── Gallery.jsx     # Fotogalerie
│   ├── Contact.jsx     # Kontakt
│   └── Footer.jsx      # Patička
├── App.jsx             # Hlavní komponenta
├── App.css             # Globální styly
├── index.css           # Reset a utility styly
└── main.jsx            # Entry point
```

## 🎨 Přizpůsobení

### Změna barev
Upravte CSS proměnné v `src/index.css`:
```css
:root {
  --primary-color: #1e3c72;
  --secondary-color: #2a5298;
  --accent-color: #ffd700;
}
```

### Změna obsahu
- **Kontaktní údaje**: upravte v `src/components/Contact.jsx` a `src/components/Footer.jsx`
- **Služby**: upravte pole `services` v `src/components/Services.jsx`
- **Galerie**: nahraďte URL obrázků v `src/components/Gallery.jsx`

### Přidání skutečných obrázků
1. Vytvořte složku `public/images/`
2. Nahrajte obrázky
3. Upravte cesty v `src/components/Gallery.jsx`

## 📱 Responzivní breakpointy

- **Desktop**: 1200px+
- **Tablet**: 768px - 1199px
- **Mobile**: < 768px

## 🔧 Další vylepšení

### Doporučené přidání:
- **Animace** - Framer Motion nebo AOS
- **Formulář** - integrace s EmailJS nebo backend API
- **Analytics** - Google Analytics
- **SEO** - React Helmet pro meta tagy
- **CMS** - Strapi nebo Contentful pro správu obsahu

## 📞 Kontakt

Pro technickou podporu nebo úpravy webu kontaktujte vývojáře.

## 📄 Licence

Tento projekt je vytvořen pro firmu Elektromontáže BARKTEK.
