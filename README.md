# Elektromontáže BARKTEK - Webové stránky

Moderní responzivní webové stránky pro firmu Elektromontáže BARKTEK vytvořené v React.js s Vite.

## 🚀 Funkce

- **Responzivní design** - optimalizováno pro všechna zařízení
- **Moderní UI/UX** - čistý a profesionální vzhled
- **Interaktivní galerie** - s modal okny pro zobrazení obrázků
- **Kontaktní formulář** - pro snadnou komunikaci s klienty
- **Smooth scrolling** - plynulé přecházení mezi sekcemi
- **SEO optimalizace** - meta tagy a strukturované data

## 📋 Sekce webu

Každá sekce má svou vlastní stránku s URL adresou:

1. **Domů** (`/`) - hlavní sekce s představením firmy
2. **Služby** (`/services`) - p<PERSON><PERSON><PERSON> poskytovaných služeb (bez emoji ikon)
3. **Galerie** (`/gallery`) - fotografie realizovaných projektů
4. **Kontakt** (`/contact`) - kontaktní informace a formulář (bez pohotovosti)
5. **Kariéra** (`/career`) - pracovní pozice a benefity
6. **Portfolio** (`/portfolio`) - seznam dokončených zakázek s filtry

### Dostupné URL adresy:
- `http://localhost:5173/` - Domovská stránka
- `http://localhost:5173/services` - Služby
- `http://localhost:5173/gallery` - Galerie
- `http://localhost:5173/contact` - Kontakt
- `http://localhost:5173/career` - Kariéra
- `http://localhost:5173/portfolio` - Portfolio

## 🛠️ Technologie

- **React 18** - moderní JavaScript framework
- **React Router** - routing pro jednotlivé stránky
- **Vite** - rychlý build tool
- **CSS3** - moderní styly s flexbox a grid
- **Google Fonts** - typografie Inter

## 🚀 Spuštění projektu

### Předpoklady
- Node.js 18+ (nebo použijte WSL)
- npm nebo yarn

### Instalace a spuštění

```bash
# Instalace závislostí
npm install

# Spuštění vývojového serveru
npm run dev

# Build pro produkci
npm run build

# Preview produkční verze
npm run preview
```

## 📁 Struktura projektu

```
src/
├── components/          # React komponenty
│   ├── Header.jsx      # Navigační header
│   ├── Hero.jsx        # Hlavní sekce
│   ├── Services.jsx    # Služby
│   ├── Gallery.jsx     # Fotogalerie
│   ├── Contact.jsx     # Kontakt
│   ├── Career.jsx/css  # Kariéra
│   ├── Portfolio.jsx/css # Portfolio
│   ├── Footer.jsx      # Patička
│   └── Layout.jsx/css  # Layout wrapper
├── pages/              # Stránky (routing)
│   ├── Home.jsx        # Domovská stránka
│   ├── Services.jsx    # Stránka služeb
│   ├── Gallery.jsx     # Stránka galerie
│   ├── Contact.jsx     # Kontaktní stránka
│   ├── Career.jsx      # Stránka kariéry
│   └── Portfolio.jsx   # Stránka portfolia
├── data/
│   └── content.js      # Centralizovaná data
├── App.jsx             # Hlavní komponenta s routingem
├── App.css             # Globální styly
├── index.css           # Reset a utility styly
└── main.jsx            # Entry point
```

## 🎨 Přizpůsobení

### Změna barev
Upravte CSS proměnné v `src/index.css`:
```css
:root {
  --primary-color: #1e3c72;
  --secondary-color: #2a5298;
  --accent-color: #ffd700;
}
```

### Změna obsahu
- **Kontaktní údaje**: upravte v `src/components/Contact.jsx` a `src/components/Footer.jsx`
- **Služby**: upravte pole `services` v `src/components/Services.jsx`
- **Galerie**: nahraďte URL obrázků v `src/components/Gallery.jsx`

### Přidání skutečných obrázků
1. Vytvořte složku `public/images/`
2. Nahrajte obrázky
3. Upravte cesty v `src/components/Gallery.jsx`

## 📱 Responzivní breakpointy

- **Desktop**: 1200px+
- **Tablet**: 768px - 1199px
- **Mobile**: < 768px

## 🔧 Další vylepšení

### Doporučené přidání:
- **Animace** - Framer Motion nebo AOS
- **Formulář** - integrace s EmailJS nebo backend API
- **Analytics** - Google Analytics
- **SEO** - React Helmet pro meta tagy
- **CMS** - Strapi nebo Contentful pro správu obsahu

## 📞 Kontakt

Pro technickou podporu nebo úpravy webu kontaktujte vývojáře.

## 📄 Licence

Tento projekt je vytvořen pro firmu Elektromontáže BARKTEK.
