.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 80px; /* Offset for fixed header */
}

.page {
  min-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
}

/* Ensure sections take full height when needed */
.page > section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Adjust Hero section for full height */
.page .hero {
  min-height: calc(100vh - 80px);
}

/* Adjust other sections for proper spacing */
.page .services,
.page .gallery,
.page .contact,
.page .career,
.page .portfolio {
  min-height: auto;
  padding: 5rem 0;
}

@media (max-width: 768px) {
  .main-content {
    padding-top: 70px;
  }
  
  .page .hero {
    min-height: calc(100vh - 70px);
  }
}
