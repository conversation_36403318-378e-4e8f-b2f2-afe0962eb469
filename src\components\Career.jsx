import './Career.css'
import { careerPositions } from '../data/content'

const Career = () => {
  return (
    <section id="career" className="career">
      <div className="container">
        <div className="section-header">
          <h2><PERSON><PERSON><PERSON></h2>
          <p>Připojte se k našemu týmu profesionálů a rozvíjejte svou kariéru v elektrotechnice</p>
        </div>
        
        <div className="career-intro">
          <div className="intro-content">
            <h3>Proč pracovat s námi?</h3>
            <div className="benefits-grid">
              <div className="benefit-item">
                <h4>Stabilní zaměstnání</h4>
                <p>Dlouhodobé projekty a stálá klientela</p>
              </div>
              <div className="benefit-item">
                <h4>Profesní růst</h4>
                <p>Možnost vzdělávání a získávání certifikací</p>
              </div>
              <div className="benefit-item">
                <h4>Moderní vybavení</h4>
                <p><PERSON>r<PERSON><PERSON> s nejnovějšími technologiemi a nástroji</p>
              </div>
              <div className="benefit-item">
                <h4>Týmová atmosféra</h4>
                <p>Přátelské pracovní prostředí a podpora kolegů</p>
              </div>
            </div>
          </div>
        </div>

        <div className="positions-section">
          <h3>Aktuální pozice</h3>
          <div className="positions-grid">
            {careerPositions.map((position) => (
              <div key={position.id} className="position-card">
                <div className="position-header">
                  <h4>{position.title}</h4>
                  <div className="position-meta">
                    <span className="position-type">{position.type}</span>
                    <span className="position-location">{position.location}</span>
                  </div>
                </div>
                
                <p className="position-description">{position.description}</p>
                
                <div className="position-details">
                  <div className="requirements">
                    <h5>Požadavky:</h5>
                    <ul>
                      {position.requirements.map((req, index) => (
                        <li key={index}>{req}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="benefits">
                    <h5>Nabízíme:</h5>
                    <ul>
                      {position.benefits.map((benefit, index) => (
                        <li key={index}>{benefit}</li>
                      ))}
                    </ul>
                  </div>
                </div>
                
                <button className="apply-button">
                  Poslat životopis
                </button>
              </div>
            ))}
          </div>
        </div>

        <div className="contact-hr">
          <h3>Kontakt pro uchazeče</h3>
          <p>
            Máte zájem o práci u nás? Pošlete nám svůj životopis na email: 
            <strong> <EMAIL></strong> nebo nás kontaktujte telefonicky.
          </p>
        </div>
      </div>
    </section>
  )
}

export default Career
