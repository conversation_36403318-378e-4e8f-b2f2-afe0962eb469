import { useState } from 'react'
import './Contact.css'

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: ''
  })

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    // Zde by byla implementace odeslání formuláře
    alert('Děkujeme za vaši zprávu! Brzy se vám ozveme.')
    setFormData({ name: '', email: '', phone: '', message: '' })
  }

  return (
    <section id="contact" className="contact">
      <div className="container">
        <div className="section-header">
          <h2>Kontaktujte nás</h2>
          <p>Máte dotaz nebo potřebujete cenovou nabídku? Neváhejte se na nás obrátit!</p>
        </div>
        
        <div className="contact-content">
          <div className="contact-info">
            <h3>Kontaktní informace</h3>
            
            <div className="contact-item">
              <div className="contact-icon">📍</div>
              <div>
                <h4>Adresa</h4>
                <p>Hlavní 123<br />123 45 Praha 1</p>
              </div>
            </div>

            <div className="contact-item">
              <div className="contact-icon">📞</div>
              <div>
                <h4>Telefon</h4>
                <p>+420 123 456 789</p>
              </div>
            </div>

            <div className="contact-item">
              <div className="contact-icon">✉️</div>
              <div>
                <h4>Email</h4>
                <p><EMAIL></p>
              </div>
            </div>

            <div className="contact-item">
              <div className="contact-icon">🕒</div>
              <div>
                <h4>Pracovní doba</h4>
                <p>Po-Pá: 7:00 - 17:00<br />So: 8:00 - 12:00<br />Ne: Zavřeno</p>
              </div>
            </div>

            <div className="emergency-contact">
              <h4>🚨 Pohotovost 24/7</h4>
              <p>Pro urgentní případy: <strong>+420 987 654 321</strong></p>
            </div>
          </div>

          <div className="contact-form">
            <h3>Napište nám</h3>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <input
                  type="text"
                  name="name"
                  placeholder="Vaše jméno *"
                  value={formData.name}
                  onChange={handleChange}
                  required
                />
              </div>
              
              <div className="form-group">
                <input
                  type="email"
                  name="email"
                  placeholder="Váš email *"
                  value={formData.email}
                  onChange={handleChange}
                  required
                />
              </div>
              
              <div className="form-group">
                <input
                  type="tel"
                  name="phone"
                  placeholder="Váš telefon"
                  value={formData.phone}
                  onChange={handleChange}
                />
              </div>
              
              <div className="form-group">
                <textarea
                  name="message"
                  placeholder="Vaše zpráva *"
                  rows="5"
                  value={formData.message}
                  onChange={handleChange}
                  required
                ></textarea>
              </div>
              
              <button type="submit" className="submit-button">
                Odeslat zprávu
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Contact
