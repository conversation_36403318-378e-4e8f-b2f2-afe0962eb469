import { useState } from 'react'
import { Link } from 'react-router-dom'
import './Header.css'
import { companyInfo, navigationItems } from '../data/content'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const closeMenu = () => {
    setIsMenuOpen(false)
  }

  return (
    <header className="header">
      <div className="container">
        <div className="logo">
          <h2>{companyInfo.name}</h2>
        </div>

        <nav className={`nav ${isMenuOpen ? 'nav-open' : ''}`}>
          <ul>
            {navigationItems.map((item, index) => (
              <li key={index}>
                <Link to={item.href} onClick={closeMenu}>
                  {item.text}
                </Link>
              </li>
            ))}
          </ul>
        </nav>

        <div className="menu-toggle" onClick={toggleMenu}>
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </header>
  )
}

export default Header
