import { useState } from 'react'
import './Header.css'
import { companyInfo, navigationItems } from '../data/content'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
    setIsMenuOpen(false)
  }

  return (
    <header className="header">
      <div className="container">
        <div className="logo">
          <h2>{companyInfo.name}</h2>
        </div>

        <nav className={`nav ${isMenuOpen ? 'nav-open' : ''}`}>
          <ul>
            {navigationItems.map((item, index) => (
              <li key={index}>
                <a href={item.href} onClick={() => scrollToSection(item.href.substring(1))}>
                  {item.text}
                </a>
              </li>
            ))}
          </ul>
        </nav>

        <div className="menu-toggle" onClick={toggleMenu}>
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </header>
  )
}

export default Header
