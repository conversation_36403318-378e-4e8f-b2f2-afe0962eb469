.not-found {
  min-height: calc(100vh - 80px);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.not-found-content {
  text-align: center;
  max-width: 500px;
  padding: 3rem 2rem;
}

.not-found-content h1 {
  font-size: 8rem;
  font-weight: 800;
  color: #1e3c72;
  margin-bottom: 1rem;
  line-height: 1;
}

.not-found-content h2 {
  font-size: 2rem;
  color: #1e3c72;
  margin-bottom: 1rem;
}

.not-found-content p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.home-button {
  background: linear-gradient(135deg, #1e3c72, #2a5298);
  color: white;
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-block;
}

.home-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(30, 60, 114, 0.3);
  color: white;
}

@media (max-width: 768px) {
  .not-found {
    min-height: calc(100vh - 70px);
  }
  
  .not-found-content h1 {
    font-size: 6rem;
  }
  
  .not-found-content h2 {
    font-size: 1.5rem;
  }
  
  .not-found-content {
    padding: 2rem 1rem;
  }
}
