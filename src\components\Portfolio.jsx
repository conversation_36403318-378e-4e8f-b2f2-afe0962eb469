import { useState } from 'react'
import { Link } from 'react-router-dom'
import './Portfolio.css'
import { portfolioProjects } from '../data/content'

const Portfolio = () => {
  const [selectedCategory, setSelectedCategory] = useState('Všechny')
  const [selectedProject, setSelectedProject] = useState(null)

  const categories = ['Všechny', 'Rezidenční', 'Komerční', 'Průmyslový']

  const filteredProjects = selectedCategory === 'Všechny' 
    ? portfolioProjects 
    : portfolioProjects.filter(project => project.category === selectedCategory)

  const openModal = (project) => {
    setSelectedProject(project)
  }

  const closeModal = () => {
    setSelectedProject(null)
  }

  return (
    <section id="portfolio" className="portfolio">
      <div className="container">
        <div className="section-header">
          <h2>Portfolio</h2>
          <p><PERSON><PERSON><PERSON><PERSON> našich dokončených projektů a realizací</p>
        </div>

        <div className="portfolio-filters">
          {categories.map((category) => (
            <button
              key={category}
              className={`filter-button ${selectedCategory === category ? 'active' : ''}`}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </button>
          ))}
        </div>

        <div className="portfolio-grid">
          {filteredProjects.map((project) => (
            <div key={project.id} className="portfolio-item" onClick={() => openModal(project)}>
              <div className="portfolio-image">
                <img src={project.image} alt={project.title} />
                <div className="portfolio-overlay">
                  <div className="portfolio-info">
                    <h4>{project.title}</h4>
                    <p className="portfolio-category">{project.category}</p>
                    <p className="portfolio-year">{project.year}</p>
                    <span className="view-details">Zobrazit detail</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {selectedProject && (
          <div className="modal" onClick={closeModal}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
              <span className="close" onClick={closeModal}>&times;</span>
              <div className="modal-body">
                <div className="modal-image">
                  <img src={selectedProject.image} alt={selectedProject.title} />
                </div>
                <div className="modal-info">
                  <h3>{selectedProject.title}</h3>
                  <div className="project-meta">
                    <span className="project-category">{selectedProject.category}</span>
                    <span className="project-year">{selectedProject.year}</span>
                  </div>
                  <p className="project-description">{selectedProject.description}</p>
                  <div className="project-services">
                    <h4>Realizované služby:</h4>
                    <div className="services-tags">
                      {selectedProject.services.map((service, index) => (
                        <span key={index} className="service-tag">{service}</span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="portfolio-cta">
          <h3>Máte podobný projekt?</h3>
          <p>Kontaktujte nás pro nezávaznou konzultaci a cenovou nabídku</p>
          <Link to="/contact" className="cta-button">
            Získat nabídku
          </Link>
        </div>
      </div>
    </section>
  )
}

export default Portfolio
