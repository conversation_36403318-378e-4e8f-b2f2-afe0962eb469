import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import './App.css'
import Layout from './components/Layout'
import Home from './pages/Home'
import ServicesPage from './pages/Services'
import GalleryPage from './pages/Gallery'
import ContactPage from './pages/Contact'
import CareerPage from './pages/Career'
import PortfolioPage from './pages/Portfolio'
import NotFound from './pages/NotFound'

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<Home />} />
            <Route path="services" element={<ServicesPage />} />
            <Route path="gallery" element={<GalleryPage />} />
            <Route path="contact" element={<ContactPage />} />
            <Route path="career" element={<CareerPage />} />
            <Route path="portfolio" element={<PortfolioPage />} />
            <Route path="*" element={<NotFound />} />
          </Route>
        </Routes>
      </div>
    </Router>
  )
}

export default App
