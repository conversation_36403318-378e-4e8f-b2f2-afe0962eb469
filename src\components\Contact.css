.contact {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.contact .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-top: 3rem;
}

.contact-info h3,
.contact-form h3 {
  font-size: 1.8rem;
  color: #1e3c72;
  margin-bottom: 2rem;
  font-weight: 700;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.contact-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
  margin-top: 0.2rem;
}

.contact-item h4 {
  margin: 0 0 0.5rem 0;
  color: #1e3c72;
  font-weight: 600;
}

.contact-item p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.emergency-contact {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  padding: 1.5rem;
  border-radius: 10px;
  text-align: center;
  margin-top: 1rem;
}

.emergency-contact h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.emergency-contact p {
  margin: 0;
  font-size: 1rem;
}

.emergency-contact strong {
  font-size: 1.1rem;
}

.contact-form {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #1e3c72;
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

.submit-button {
  background: linear-gradient(135deg, #1e3c72, #2a5298);
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.submit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(30, 60, 114, 0.3);
}

/* Mobile styles */
@media (max-width: 768px) {
  .contact {
    padding: 3rem 0;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .contact-info h3,
  .contact-form h3 {
    font-size: 1.5rem;
  }

  .contact-item {
    padding: 1rem;
  }

  .contact-form {
    padding: 1.5rem;
  }

  .form-group input,
  .form-group textarea {
    padding: 0.8rem;
  }

  .submit-button {
    padding: 0.8rem 1.5rem;
  }
}
