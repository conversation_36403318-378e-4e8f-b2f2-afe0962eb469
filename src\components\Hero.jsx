import './Hero.css'
import { companyInfo, heroFeatures } from '../data/content'

const Hero = () => {
  const scrollToContact = () => {
    const element = document.getElementById('contact')
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section id="home" className="hero">
      <div className="hero-overlay">
        <div className="container">
          <div className="hero-content">
            <h1>{companyInfo.slogan}</h1>
            <h2>{companyInfo.name.split(' ')[1]}</h2>
            <p>{companyInfo.description}</p>
            <div className="hero-features">
              {heroFeatures.map((feature, index) => (
                <div key={index} className="feature">
                  <span className="feature-icon">{feature.icon}</span>
                  <span>{feature.text}</span>
                </div>
              ))}
            </div>
            <button className="cta-button" onClick={scrollToContact}>
              Získat nabídku
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
