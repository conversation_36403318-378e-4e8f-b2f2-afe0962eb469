.portfolio {
  padding: 5rem 0;
  background: #f8f9fa;
}

.portfolio .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.portfolio-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.filter-button {
  background: white;
  color: #666;
  border: 2px solid #e9ecef;
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.filter-button:hover {
  border-color: #1e3c72;
  color: #1e3c72;
}

.filter-button.active {
  background: #1e3c72;
  color: white;
  border-color: #1e3c72;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.portfolio-item {
  cursor: pointer;
  border-radius: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.portfolio-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.portfolio-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.portfolio-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-image img {
  transform: scale(1.1);
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent, rgba(30, 60, 114, 0.9));
  display: flex;
  align-items: flex-end;
  padding: 1.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-info {
  color: white;
}

.portfolio-info h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.portfolio-category,
.portfolio-year {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

.view-details {
  display: inline-block;
  margin-top: 0.5rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: #ffd700;
}

/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  background: white;
  border-radius: 15px;
  overflow: hidden;
  animation: slideIn 0.3s ease;
}

.modal-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  max-height: 80vh;
}

.modal-image {
  background: #f8f9fa;
}

.modal-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.modal-info {
  padding: 2rem;
  overflow-y: auto;
}

.modal-info h3 {
  color: #1e3c72;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.project-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.project-category,
.project-year {
  background: #e9ecef;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.9rem;
  color: #666;
}

.project-category {
  background: #d4edda;
  color: #155724;
}

.project-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 2rem;
}

.project-services h4 {
  color: #1e3c72;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.services-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.service-tag {
  background: #1e3c72;
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.close {
  position: absolute;
  top: 15px;
  right: 20px;
  color: white;
  font-size: 2rem;
  font-weight: bold;
  cursor: pointer;
  z-index: 2001;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.close:hover {
  background: rgba(0, 0, 0, 0.8);
}

.portfolio-cta {
  text-align: center;
  background: white;
  padding: 3rem 2rem;
  border-radius: 15px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.portfolio-cta h3 {
  color: #1e3c72;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.portfolio-cta p {
  color: #666;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.cta-button {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #1e3c72;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* Mobile styles */
@media (max-width: 768px) {
  .portfolio {
    padding: 3rem 0;
  }

  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .portfolio-filters {
    gap: 0.5rem;
  }

  .filter-button {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }

  .modal-body {
    grid-template-columns: 1fr;
    max-height: 95vh;
  }

  .modal-info {
    padding: 1.5rem;
  }

  .portfolio-cta {
    padding: 2rem 1rem;
  }

  .portfolio-cta h3 {
    font-size: 1.5rem;
  }
}
