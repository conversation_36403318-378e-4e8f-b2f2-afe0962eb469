.hero {
  height: 100vh;
  background: linear-gradient(rgba(30, 60, 114, 0.8), rgba(42, 82, 152, 0.8)),
              url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><pattern id="electrical" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="50" cy="50" r="2" fill="%23ffffff" opacity="0.1"/><path d="M20,50 L80,50 M50,20 L50,80" stroke="%23ffffff" stroke-width="1" opacity="0.05"/></pattern></defs><rect width="100%" height="100%" fill="url(%23electrical)"/></svg>');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  color: white;
  position: relative;
}

.hero-overlay {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.hero .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.hero-content {
  max-width: 600px;
  text-align: center;
  margin: 0 auto;
}

.hero h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.hero h2 {
  font-size: 2.5rem;
  font-weight: 800;
  color: #ffd700;
  margin-bottom: 1.5rem;
  letter-spacing: 2px;
}

.hero p {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.feature {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.feature-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.feature span:last-child {
  font-weight: 600;
  font-size: 0.9rem;
}

.cta-button {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #1e3c72;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 700;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 215, 0, 0.3);
}

/* Mobile styles */
@media (max-width: 768px) {
  .hero h1 {
    font-size: 2.5rem;
  }

  .hero h2 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .hero-features {
    flex-direction: column;
    gap: 1rem;
  }

  .feature {
    flex-direction: row;
    justify-content: center;
  }

  .feature-icon {
    font-size: 1.5rem;
    margin-bottom: 0;
    margin-right: 0.5rem;
  }
}
